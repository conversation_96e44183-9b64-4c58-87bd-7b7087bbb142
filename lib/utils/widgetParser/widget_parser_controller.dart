import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'dart:convert';
import 'package:tangoworkplace/common/common_import.dart';
import 'package:tangoworkplace/common/widgets/component_widgets/tadropdown.dart';
import 'package:tangoworkplace/utils/widgetParser/json_widget.dart';
import 'package:tangoworkplace/utils/widgetParser/json_widget_types.dart';

class WidgetParserController extends GetxController {
  RxMap<String, dynamic> jsonData = <String, dynamic>{}.obs;
  RxList<JsonWidget> jwidgetList = <JsonWidget>[].obs;
  var isLoading = false.obs;
  final List<TextEditingController> textEditControllers = [];

  @override
  void onInit() {
    super.onInit();
    getWidgetJsonData();
  }

  Widget parseJson() {
    if (jsonData.isEmpty) {
      return const Center(child: CircularProgressIndicator());
    }

    return buildWidget(JsonWidget.fromJson(jsonData));
  }

  Widget buildWidget(JsonWidget jsonWidget) {
    debugPrint('Building widget of type: ${jsonWidget.widgetType}');
    final widgetType = JsonWidgetTypeHelper.getType(jsonWidget.widgetType ?? 'text');

    switch (widgetType) {
      case JsonWidgetType.scaffold:
        return _buildScaffoldWidget(jsonWidget);
      case JsonWidgetType.textView:
        return _buildTextWidget(jsonWidget);
      case JsonWidgetType.row:
        return _buildRowWidget(jsonWidget);
      case JsonWidgetType.column:
        return _buildColumnWidget(jsonWidget);
      case JsonWidgetType.taInput:
        return _buildTaInput(jsonWidget);
      case JsonWidgetType.taDropDown:
        return _buildTaDropDown(jsonWidget);
      case JsonWidgetType.taCheckBox:
        return _buildTaCheckBox(jsonWidget);
      case JsonWidgetType.appBar:
        return _buildAppBarWidget(jsonWidget);
      case JsonWidgetType.tabBar:
        return _buildTabBarWidget(jsonWidget);
      case JsonWidgetType.tabBarView:
        return _buildTabBarViewWidget(jsonWidget);
    }
  }

  Widget _buildScaffoldWidget(JsonWidget jsonWidget) {
    return Scaffold(
      appBar: jsonWidget.appBar != null ? buildWidget(jsonWidget.appBar!) as AppBar : null,
      body: jsonWidget.body != null ? buildWidget(jsonWidget.body!) : Container(),
    );
  }

  AppBar _buildAppBarWidget(JsonWidget jsonWidget) {
    return AppBar(
      title: Text(jsonWidget.text ?? 'Form'),
      backgroundColor: Colors.white,
      elevation: 2,
    );
  }

  Widget _buildTextWidget(JsonWidget jsonWidget) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0, horizontal: 16.0),
      child: Text(
        jsonWidget.text ?? '',
        style: TextStyle(
          fontSize: jsonWidget.fontSize?.toDouble() ?? 16.0,
          color: jsonWidget.color != null ? Color(int.parse(jsonWidget.color!)) : Colors.black87,
          fontWeight: jsonWidget.label != null ? FontWeight.bold : FontWeight.normal,
        ),
      ),
    );
  }

  Widget _buildColumnWidget(JsonWidget jsonWidget) {
    final crossAxisAlignment = CrossAxisAlignment.values.firstWhere(
      (element) => element.name.toLowerCase() == (jsonWidget.crossAxisAligment?.toLowerCase() ?? 'start'),
      orElse: () => CrossAxisAlignment.start,
    );
    final mainAxisAlignment = MainAxisAlignment.values.firstWhere(
      (element) => element.name.toLowerCase() == (jsonWidget.mainAxisAligment?.toLowerCase() ?? 'start'),
      orElse: () => MainAxisAlignment.start,
    );
    final mainAxisSize = MainAxisSize.values.firstWhere(
      (element) => element.name.toLowerCase() == (jsonWidget.mainAxisSize?.toLowerCase() ?? 'max'),
      orElse: () => MainAxisSize.max,
    );

    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        mainAxisAlignment: mainAxisAlignment,
        crossAxisAlignment: crossAxisAlignment,
        mainAxisSize: mainAxisSize,
        children: [
          if (jsonWidget.label != null && jsonWidget.label!.isNotEmpty)
            _buildTextWidget(JsonWidget(
              widgetType: 'textView',
              text: jsonWidget.label,
              fontSize: 20,
            )),
          if (jsonWidget.children != null) ...jsonWidget.children!.map((e) => buildWidget(e)).toList(),
        ],
      ),
    );
  }

  Widget _buildRowWidget(JsonWidget jsonWidget) {
    final crossAxisAlignment = CrossAxisAlignment.values.firstWhere(
      (element) => element.name.toLowerCase() == (jsonWidget.crossAxisAligment?.toLowerCase() ?? 'start'),
      orElse: () => CrossAxisAlignment.start,
    );
    final mainAxisAlignment = MainAxisAlignment.values.firstWhere(
      (element) => element.name.toLowerCase() == (jsonWidget.mainAxisAligment?.toLowerCase() ?? 'start'),
      orElse: () => MainAxisAlignment.start,
    );
    final mainAxisSize = MainAxisSize.values.firstWhere(
      (element) => element.name.toLowerCase() == (jsonWidget.mainAxisSize?.toLowerCase() ?? 'max'),
      orElse: () => MainAxisSize.max,
    );

    return Row(
      mainAxisAlignment: mainAxisAlignment,
      crossAxisAlignment: crossAxisAlignment,
      mainAxisSize: mainAxisSize,
      children: jsonWidget.children?.map((e) => buildWidget(e)).toList() ?? [],
    );
  }

  Widget _buildTabBarWidget(JsonWidget jsonWidget) {
    return TabBar(
      labelColor: Colors.black87,
      unselectedLabelColor: Colors.grey,
      indicatorColor: Colors.blue,
      tabs: jsonWidget.tabs?.map((tab) => Tab(text: tab.label ?? 'Tab')).toList() ?? [],
    );
  }

  Widget _buildTabBarViewWidget(JsonWidget jsonWidget) {
    return Expanded(
      child: TabBarView(
        children: jsonWidget.tabs
                ?.map((tab) => _buildColumnWidget(JsonWidget(
                      widgetType: 'column',
                      children: tab.sections
                          ?.map((section) => JsonWidget.fromJson({
                                'widgetType': 'column',
                                'label': section.label,
                                'children': section.fields
                                    ?.map((field) => JsonWidget.fromJson({
                                          'widgetType': _determineWidgetType(field as Map<String, dynamic>),
                                          'text': field.label,
                                          'value': field.value,
                                          'readOnly': true, //field.editable == 'FALSE',
                                        }))
                                    .toList(),
                              }))
                          .toList(),
                    )))
                .toList() ??
            [],
      ),
    );
  }

  Widget _buildTaCheckBox(JsonWidget jsonWidget) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0, horizontal: 16.0),
      child: TaCheckBox(
        label: jsonWidget.text,
        value: jsonWidget.value is bool ? (jsonWidget.value ?? false) : false,
        onChanged: jsonWidget.readOnly == true ? null : (value) {},
      ),
    );
  }

  Widget _buildTaDropDown(JsonWidget jsonWidget) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0, horizontal: 16.0),
      child: TaFormDropdown(
        listflag: true,
        emptttext: '',
        label: jsonWidget.text,
        readonly: jsonWidget.readOnly ?? false,
        value: jsonWidget.value,
        items: jsonWidget.dropDownValues
                ?.map((element) => DropdownMenuItem(
                      value: element.value,
                      child: element.value, // buildWidget(element.widget),
                    ))
                .toList() ??
            [],
        onChanged: jsonWidget.readOnly == true ? null : (_) {},
        onSaved: (_) {},
      ),
    );
  }

  Widget _buildTaInput(JsonWidget jsonWidget) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0, horizontal: 16.0),
      child: TaInputText(
        title: jsonWidget.text,
        value: jsonWidget.value is String ? (jsonWidget.value ?? '') : '',
        readOnly: jsonWidget.readOnly ?? false,
        controller: TextEditingController(text: jsonWidget.value?.toString()),
        onChanged: jsonWidget.readOnly == true ? null : (_) {},
      ),
    );
  }

  String _determineWidgetType(Map<String, dynamic> item) {
    String label = item['label']?.toString().toLowerCase() ?? '';
    dynamic value = item['val'];

    if (item['widgetType'] != null) return item['widgetType'];

    if (value is bool || label.contains('is ') || label.contains('?') || label.contains('checklist')) {
      return 'taCheckBox';
    }

    if (label.contains('status') ||
        label.contains('type') ||
        label.contains('classification') ||
        label.contains('option')) {
      return 'taDropDown';
    }

    if (value is num ||
        label.contains('id') ||
        label.contains('ratio') ||
        label.contains('psf') ||
        label.contains('total') ||
        label.contains('cost') ||
        label.contains('fte') ||
        label.contains('months') ||
        label.contains('years')) {
      return 'taInput';
    }

    return 'taInput';
  }

  Future<void> getWidgetJsonData() async {
    isLoading.value = true;
    try {
      Map<String, dynamic> dynamicMap = {
        'a': ['site_id', 'EXACT', '196819'],
      };
      var filter = CommonServices.getFilter(dynamicMap);
      var url = '$dynamicattributesurl/SITE/DYNAMIC_ATTRIBUTES';

      var resMap = await ApiService.post(url, payloadObj: filter);
      if (resMap != null) {
        resMap = jsonDecode(resMap);
        jsonData.value = {
          //'widgetType': 'scaffold',
          //'appBar': {'widgetType': 'appBar', 'text': 'Dynamic Form'},
          //'body': {
          'widgetType': 'tabBarView',
          'tabs': (resMap['componentList']?['tabs'] as List<dynamic>?)?.map((tab) {
                return {
                  'label': tab['label'],
                  'sections': (tab['sections'] as List<dynamic>?)?.map((section) {
                        return {
                          'label': section['label'],
                          'fields': (section['fields'] as List<dynamic>?)?.map((field) {
                                return {
                                  'widgetType': _determineWidgetType(field),
                                  'text': field['label'],
                                  'value': field['val'],
                                  'readOnly': field['editable'] == 'FALSE',
                                };
                              }).toList() ??
                              [],
                        };
                      }).toList() ??
                      [],
                };
              }).toList() ??
              [],
          // },
        };
      }
    } catch (e) {
      debugPrint('Error fetching widget JSON data: $e');
    } finally {
      isLoading.value = false;
    }
  }
}

// Update JsonWidget class to support tabs
class JsonWidget {
  final String? widgetType;
  final String? text;
  final String? color;
  final num? fontSize;
  final String? crossAxisAligment;
  final String? mainAxisAligment;
  final String? mainAxisSize;
  final dynamic value;
  final bool? readOnly;
  final List<JsonWidget>? children;
  final JsonWidget? appBar;
  final JsonWidget? body;
  final List<JsonWidget>? dropDownValues;
  final List<JsonTab>? tabs;
  final String? label;

  JsonWidget({
    this.widgetType,
    this.text,
    this.color,
    this.fontSize,
    this.crossAxisAligment,
    this.mainAxisAligment,
    this.mainAxisSize,
    this.value,
    this.readOnly,
    this.children,
    this.appBar,
    this.body,
    this.dropDownValues,
    this.tabs,
    this.label,
  });

  factory JsonWidget.fromJson(Map<String, dynamic> json) {
    return JsonWidget(
      widgetType: json['widgetType']?.toString(),
      text: json['text']?.toString(),
      color: json['color']?.toString(),
      fontSize: json['fontSize'] is num ? json['fontSize'] : null,
      crossAxisAligment: json['crossAxisAligment']?.toString(),
      mainAxisAligment: json['mainAxisAligment']?.toString(),
      mainAxisSize: json['mainAxisSize']?.toString(),
      value: json['val'],
      readOnly: json['readOnly'] is bool ? json['readOnly'] : null,
      children:
          json['children'] != null ? (json['children'] as List).map((e) => JsonWidget.fromJson(e)).toList() : null,
      appBar: json['appBar'] != null ? JsonWidget.fromJson(json['appBar']) : null,
      body: json['body'] != null ? JsonWidget.fromJson(json['body']) : null,
      dropDownValues: json['dropDownValues'] != null
          ? (json['dropDownValues'] as List)
              .map((e) => JsonWidget.fromJson({
                    'value': e['value'],
                    'widget': e['widget'] != null ? JsonWidget.fromJson(e['widget']) : null,
                  }))
              .toList()
          : null,
      tabs: json['tabs'] != null ? (json['tabs'] as List).map((e) => JsonTab.fromJson(e)).toList() : null,
      label: json['label']?.toString(),
    );
  }
}

class JsonTab {
  final String? label;
  final List<JsonSection>? sections;

  JsonTab({this.label, this.sections});

  factory JsonTab.fromJson(Map<String, dynamic> json) {
    return JsonTab(
      label: json['label']?.toString(),
      sections:
          json['sections'] != null ? (json['sections'] as List).map((e) => JsonSection.fromJson(e)).toList() : null,
    );
  }
}

class JsonSection {
  final String? label;
  final List<JsonWidget>? fields;

  JsonSection({this.label, this.fields});

  factory JsonSection.fromJson(Map<String, dynamic> json) {
    return JsonSection(
      label: json['label']?.toString(),
      fields: json['fields'] != null ? (json['fields'] as List).map((e) => JsonWidget.fromJson(e)).toList() : null,
    );
  }
}

// Update JsonWidgetType enum
enum JsonWidgetType {
  scaffold,
  textView,
  row,
  column,
  taInput,
  taDropDown,
  taCheckBox,
  appBar,
  tabBar,
  tabBarView,
}

class JsonWidgetTypeHelper {
  static JsonWidgetType getType(String type) {
    switch (type.toLowerCase()) {
      case 'scaffold':
        return JsonWidgetType.scaffold;
      case 'textview':
        return JsonWidgetType.textView;
      case 'row':
        return JsonWidgetType.row;
      case 'column':
        return JsonWidgetType.column;
      case 'tainput':
        return JsonWidgetType.taInput;
      case 'tadropdown':
        return JsonWidgetType.taDropDown;
      case 'tacheckbox':
        return JsonWidgetType.taCheckBox;
      case 'appbar':
        return JsonWidgetType.appBar;
      case 'tabbar':
        return JsonWidgetType.tabBar;
      case 'tabbarview':
        return JsonWidgetType.tabBarView;
      default:
        return JsonWidgetType.textView;
    }
  }
}
