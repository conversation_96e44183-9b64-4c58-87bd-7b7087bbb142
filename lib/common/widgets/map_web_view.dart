import 'dart:io';

import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:flutter_inappwebview/flutter_inappwebview.dart';
import 'package:flutter_downloader/flutter_downloader.dart';
import 'package:open_file/open_file.dart';
import 'package:path_provider/path_provider.dart';
import 'package:permission_handler/permission_handler.dart';

import '../../utils/common_utils.dart';
import '../../utils/constvariables.dart';
import '../../utils/preferences_utils.dart';
import '../component_utils.dart';
import '../progess_indicator_cust.dart';

class MapWebView extends StatefulWidget {
  final String url;
  final String title;
  final String? source;
  final String host;

  const MapWebView(this.url, this.title, this.host, {this.source, super.key});

  @override
  _MapWebViewState createState() => _MapWebViewState(url, title, source, host);
}

class _MapWebViewState extends State<MapWebView> {
  final String _url;
  final String _title;
  final String? _source;
  String _host;
  final _key = UniqueKey();
  final GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();
  InAppWebViewController? _webViewController;
  InAppWebViewController? _webViewPopupController;
  final CookieManager _cookieManager = CookieManager.instance();
  num position = 1; // For IndexedStack (0 = content, 1 = loading)

  _MapWebViewState(this._url, this._title, this._source, this._host);
  //late Saf saf;

  @override
  void initState() {
    super.initState();
    _host = _host.replaceAll('https://', '');
    debugPrint(_host);
    debugPrint(_url);
    debugPrint('---------------- $_source');

    // Initialize Flutter Downloader only if not already initialized
    _initializeDownloader();

    // Request location permissions for Android
    _requestLocationPermissions();

    // Clear cookies
    _cookieManager.deleteAllCookies();
  }

  Future<void> _initializeDownloader() async {
    try {
      // Try to initialize the downloader
      await FlutterDownloader.initialize(debug: true);
    } catch (e) {
      // If already initialized, just log and continue
      debugPrint('FlutterDownloader already initialized: $e');
    }
  }

  Future<void> _requestPermissions() async {
    if (await Permission.storage.request().isDenied) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Storage permission is required to download files')),
      );
    }
  }

  Future<void> _requestLocationPermissions() async {
    if (Platform.isAndroid) {
      // Request location permissions for Android
      var locationStatus = await Permission.location.request();
      var locationWhenInUseStatus = await Permission.locationWhenInUse.request();

      debugPrint('Location permission status: $locationStatus');
      debugPrint('Location when in use permission status: $locationWhenInUseStatus');

      if (locationStatus.isDenied && locationWhenInUseStatus.isDenied) {
        debugPrint('Location permissions denied for WebView');
      }
    }
  }

  Future<void> checkWritePermission() async {
    if (!kIsWeb) {
      if (Platform.isAndroid || Platform.isIOS) {
        var permissionStatus = await Permission.storage.status;

        switch (permissionStatus) {
          case PermissionStatus.denied:
          case PermissionStatus.permanentlyDenied:
            await Permission.storage.request();
            break;
          default:
        }

        // await saf.getDirectoryPermission(isDynamic: true);
        debugPrint('permission status: $permissionStatus');
      }
    }
  }

  @override
  void dispose() {
    SystemChrome.setPreferredOrientations([
      DeviceOrientation.landscapeRight,
      DeviceOrientation.landscapeLeft,
      DeviceOrientation.portraitUp,
      DeviceOrientation.portraitDown,
    ]);
    _cookieManager.deleteAllCookies();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      key: _scaffoldKey,
      appBar: AppBar(
        iconTheme: Theme.of(context).appBarTheme.iconTheme,
        title: Text(
          _title,
          style: ComponentUtils.appbartitlestyle,
        ),
        backgroundColor: Colors.white,
        elevation: 4,
        leading: IconButton(
          icon: ComponentUtils.backpageIcon,
          color: CommonUtils.createMaterialColor(const Color(0xFFb10c00)),
          onPressed: () {
            debugPrint('------back-------');
            Get.back();
          },
        ),
      ),
      body: SafeArea(
        child: IndexedStack(
          index: position as int?,
          children: [
            Stack(
              children: [
                Align(
                  alignment: Alignment.topCenter,
                  child: Container(),
                ),
                Positioned(
                  top: 0,
                  right: 0,
                  left: 0,
                  bottom: 0,
                  child: ClipRRect(
                    child: Container(
                      child: Column(
                        children: [
                          Expanded(
                            child: InAppWebView(
                              key: _key,
                              initialUrlRequest: URLRequest(url: WebUri.uri(Uri.parse(_url))),
                              initialSettings: InAppWebViewSettings(
                                supportMultipleWindows: true,
                                javaScriptEnabled: true,
                                useOnDownloadStart: true,
                                useHybridComposition: true,
                                supportZoom: true,
                                builtInZoomControls: true,
                                displayZoomControls: false,
                                javaScriptCanOpenWindowsAutomatically: false, // Prevent automatic new windows
                                // Geolocation settings
                                geolocationEnabled: true,
                                // Android-specific settings
                                domStorageEnabled: true,
                                databaseEnabled: true,
                                // Allow mixed content for location services
                                mixedContentMode: MixedContentMode.MIXED_CONTENT_ALWAYS_ALLOW,
                                allowFileAccess: true,
                                allowContentAccess: true,
                              ),
                              onWebViewCreated: (controller) async {
                                _webViewController = controller;

                                // Get session ID
                                var sessionId = await SharedPrefUtils.readPrefStr(ConstHelper.sessionIdvar);
                                debugPrint('sessionid      $sessionId');

                                try {
                                  // Set session storage
                                  await controller.evaluateJavascript(
                                    source: 'sessionStorage.setItem("JSESSIONID", "$sessionId");',
                                  );

                                  // Enable geolocation for Android
                                  if (Platform.isAndroid) {
                                    await controller.evaluateJavascript(
                                      source: '''
                                        if (navigator.geolocation) {
                                          console.log('Geolocation is supported');
                                        } else {
                                          console.log('Geolocation is not supported');
                                        }
                                      ''',
                                    );
                                  }
                                } catch (e) {
                                  debugPrint('JavaScript error: $e');
                                }

                                // Set cookie
                                await _cookieManager.setCookie(
                                  url: WebUri.uri(Uri.parse('https://$_host')),
                                  name: 'JSESSIONID',
                                  value: sessionId,
                                  domain: _host,
                                  expiresDate: DateTime.now().add(const Duration(minutes: 30)).millisecondsSinceEpoch,
                                  isHttpOnly: false,
                                );
                                _webViewController?.addJavaScriptHandler(
                                    handlerName: 'navigateFromMaps',
                                    callback: (args) {
                                      debugPrint('JavaScript handler called');
                                      if (args.isNotEmpty && args[0] is Map) {
                                        final Map<dynamic, dynamic> params = args[0] as Map<dynamic, dynamic>;
                                        final String pageId = params['pageId']?.toString() ?? 'Unknown';
                                        final String userData = params['userData']?.toString() ?? 'No User';
                                        // Navigate to SecondPage with named parameters
                                        Navigator.pushNamed(
                                          context,
                                          '/secondPage',
                                          arguments: {'pageId': pageId, 'userData': userData},
                                        );
                                      }
                                    });
                              },
                              onPermissionRequest: (controller, permissionRequest) async {
                                debugPrint('Permission requested: ${permissionRequest.resources}');

                                // Handle geolocation permission
                                if (permissionRequest.resources.contains(PermissionResourceType.GEOLOCATION)) {
                                  // Check if we have location permission
                                  var locationStatus = await Permission.location.status;
                                  var locationWhenInUseStatus = await Permission.locationWhenInUse.status;

                                  if (locationStatus.isGranted || locationWhenInUseStatus.isGranted) {
                                    return PermissionResponse(
                                      resources: permissionRequest.resources,
                                      action: PermissionResponseAction.GRANT,
                                    );
                                  } else {
                                    // Request permission from user
                                    var newLocationStatus = await Permission.location.request();
                                    if (newLocationStatus.isGranted) {
                                      return PermissionResponse(
                                        resources: permissionRequest.resources,
                                        action: PermissionResponseAction.GRANT,
                                      );
                                    } else {
                                      return PermissionResponse(
                                        resources: permissionRequest.resources,
                                        action: PermissionResponseAction.DENY,
                                      );
                                    }
                                  }
                                }

                                // Default: grant other permissions
                                return PermissionResponse(
                                  resources: permissionRequest.resources,
                                  action: PermissionResponseAction.GRANT,
                                );
                              },
                              onCreateWindow: (controller, createWindowRequest) async {
                                final url = createWindowRequest.request.url?.toString() ?? '';
                                debugPrint('New window requested: $url');
                                debugPrint('Headers: ${createWindowRequest.request.headers}');
                                // if (await Permission.storage.request().isGranted) {
                                showDialog(
                                  context: context,
                                  builder: (context) {
                                    return AlertDialog(
                                      contentPadding: EdgeInsets.zero,
                                      content: SizedBox(
                                        width: 100.0, //MediaQuery.of(context).size.width * 0.4,
                                        height: 60.0, //MediaQuery.of(context).size.height * 0.4,
                                        child: Column(
                                          children: [
                                            const SizedBox(height: 10.0),
                                            const ProgressIndicatorCust(),
                                            SizedBox(
                                              width: 100.0, //MediaQuery.of(context).size.width * 0.4,
                                              height: 1.0, //MediaQuery.of(context).size.height * 0.4,
                                              child: InAppWebView(
                                                windowId: createWindowRequest.windowId,
                                                onWebViewCreated: (InAppWebViewController controller) {
                                                  debugPrint('dialog webview created');
                                                  _webViewPopupController = controller;
                                                },
                                                onDownloadStartRequest:
                                                    (controller, DownloadStartRequest downloadStartRequest) async {
                                                  await _handleDownloadStartRequest(downloadStartRequest);
                                                },
                                                onLoadStart: (controller, url) async {
                                                  var sessionId =
                                                      await SharedPrefUtils.readPrefStr(ConstHelper.sessionIdvar);
                                                  await _cookieManager.setCookie(
                                                    url: WebUri.uri(Uri.parse('https://$_host')),
                                                    name: 'JSESSIONID',
                                                    value: sessionId,
                                                    domain: _host,
                                                    path: '/',
                                                    isSecure: true,
                                                  );
                                                  debugPrint('Load started: $url');
                                                  // setState(() {
                                                  //   position = 1; // Show progress indicator
                                                  // });
                                                },
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                    );
                                  },
                                );

                                return true; // Allow new window
                                // } else {
                                //   await openAppSettings();
                                //   ComponentUtils.showsnackbar(
                                //     text: 'Storage permission denied',
                                //     heading: 'Error',
                                //     seconds: 2,
                                //   );

                                //   return false;
                                // }
                              },
                              onDownloadStartRequest: (controller, downloadStartRequest) async {
                                debugPrint('Handle download');
                                // Handle download
                                final url = downloadStartRequest.url.toString();
                                final suggestedFilename = downloadStartRequest.suggestedFilename ?? 'download.png';

                                // Request storage permission
                                if (await Permission.storage.request().isGranted) {
                                  // Get directory to save the file
                                  final dir = await getApplicationDocumentsDirectory();
                                  final filePath = '${dir.path}/$suggestedFilename';

                                  // Download file using Dio
                                  try {
                                    final dio = Dio();
                                    await dio.download(url, filePath);
                                    debugPrint('File downloaded to $filePath');
                                    ComponentUtils.showsnackbar(text: "Comment added successfully...");
                                    // Notify user or open the file
                                  } catch (e) {
                                    debugPrint('Download error: $e');
                                  }
                                } else {
                                  debugPrint('Storage permission denied');
                                }
                              },

                              onConsoleMessage: (controller, consoleMessage) {
                                debugPrint('Console: ${consoleMessage.message}');
                              },
                              onLoadStart: (controller, url) async {
                                var sessionId = await SharedPrefUtils.readPrefStr(ConstHelper.sessionIdvar);
                                await _cookieManager.setCookie(
                                  url: WebUri.uri(Uri.parse('https://$_host')),
                                  name: 'JSESSIONID',
                                  value: sessionId,
                                  domain: _host,
                                  path: '/',
                                  isSecure: true,
                                );
                                debugPrint('Load started: $url');
                                setState(() {
                                  position = 1; // Show progress indicator
                                });
                              },
                              onLoadStop: (controller, url) {
                                debugPrint('Load stopped: $url');
                                setState(() {
                                  position = 0; // Hide progress indicator
                                });
                              },
                              onReceivedError: (controller, request, error) {
                                // Check if this is a download-related error
                                final url = request.url.toString();
                                final isDownloadUrl = url.contains('/download') ||
                                    url.contains('/rest/reports/download') ||
                                    url.endsWith('.pdf');

                                // Ignore WebKitErrorDomain code 102 errors when downloading
                                if (error.type == WebResourceErrorType.CANCELLED &&
                                    error.description.contains('code=102') &&
                                    (isDownloadUrl || request.isForMainFrame == false)) {
                                  debugPrint('Ignoring expected download interruption: ${error.description}');
                                  return;
                                }

                                // Handle other errors
                                debugPrint('WebView error: ${error.description}');

                                // Don't show error for download URLs
                                if (!isDownloadUrl) {
                                  ScaffoldMessenger.of(context).showSnackBar(
                                    SnackBar(content: Text('Page load error: ${error.description}')),
                                  );
                                }
                              },
                              // onDownloadStartRequest: (controller, request) async {
                              //   final url = request.url.toString();
                              //   final suggestedFilename = request.suggestedFilename ?? url.split('/').last;
                              //   debugPrint(
                              //       'Download start request: $url, Filename: $suggestedFilename, MIME: ${request.mimeType}');
                              //   await _handleDownload(url, suggestedFilename);
                              // },
                              gestureRecognizers: {
                                Factory<VerticalDragGestureRecognizer>(() => VerticalDragGestureRecognizer()),
                                Factory<HorizontalDragGestureRecognizer>(() => HorizontalDragGestureRecognizer()),
                                Factory<TapGestureRecognizer>(() => TapGestureRecognizer()),
                              },
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ],
            ),
            const ProgressIndicatorCust(),
          ],
        ),
      ),
    );
  }

  Future<void> _handleDownload(String url, String suggestedFilename) async {
    debugPrint('Initiating download: $url - $suggestedFilename');

    // Show download starting notification
    ScaffoldMessenger.of(context).clearSnackBars();
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('Starting download: $suggestedFilename')),
    );

    Directory? directory;
    try {
      if (Platform.isAndroid) {
        directory = await getExternalStorageDirectory();
      } else if (Platform.isIOS) {
        directory = await getApplicationDocumentsDirectory();
      }

      if (directory == null) {
        debugPrint('Storage directory unavailable');
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Unable to access storage')),
        );
        return;
      }

      try {
        final taskId = await FlutterDownloader.enqueue(
          url: url,
          savedDir: directory.path,
          fileName: suggestedFilename,
          showNotification: true,
          openFileFromNotification: true,
          saveInPublicStorage: Platform.isAndroid,
        );

        debugPrint('Download enqueued with taskId: $taskId for URL: $url');

        // Show success notification
        ScaffoldMessenger.of(context).clearSnackBars();
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Downloading: $suggestedFilename'),
            duration: const Duration(seconds: 2),
          ),
        );
      } catch (e) {
        debugPrint('Download failed: $e');
        ScaffoldMessenger.of(context).clearSnackBars();
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Download failed: $e')),
        );
      }
    } catch (e) {
      debugPrint('Storage error: $e');
      ScaffoldMessenger.of(context).clearSnackBars();
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Storage error: $e')),
      );
    }
  }

  Future<void> _handleDownloadStartRequest(DownloadStartRequest downloadStartRequest) async {
    debugPrint('Handle download: ${downloadStartRequest.url}');
    final url = downloadStartRequest.url.toString();
    var suggestedFilename = downloadStartRequest.suggestedFilename ?? '';

    Directory? directory;
    try {
      if (Platform.isAndroid) {
        debugPrint('content : ${downloadStartRequest!.contentDisposition}');
        var filename = extractFileName(downloadStartRequest.contentDisposition ?? '');
        directory =
            await getExternalStorageDirectory(); // Or use getApplicationDocumentsDirectory() for private storage
        debugPrint('android sdirectory: ${directory!.path}');
        // Initialize Dio
        final dio = Dio();
        final sessionId = await SharedPrefUtils.readPrefStr(ConstHelper.sessionIdvar);

        // Set download path
        final savePath = '${directory!.path}/$filename';

        // Perform download with Dio
        await dio.download(
          url,
          savePath,
          options: Options(
            headers: {'Cookie': 'JSESSIONID=$sessionId'},
          ),
          onReceiveProgress: (received, total) {
            if (total != -1) {
              debugPrint('Download progress: ${(received / total * 100).toStringAsFixed(0)}%');
            }
          },
        );

        Get.back();
        ComponentUtils.showwarnpopup(
          title: 'Info',
          contenttext: 'File downloaded: $filename',
          confirmBtn: 'Open',
          cancelBtn: 'Close',
          confirmfunction: () {
            // Use File to open the downloaded file
            final file = File(savePath);
            if (file.existsSync()) {
              OpenFile.open(savePath);
            }
            Get.back();
          },
        );
      } else if (Platform.isIOS) {
        directory = await getApplicationDocumentsDirectory();

        if (directory != null) {
          var sessionId = await SharedPrefUtils.readPrefStr(ConstHelper.sessionIdvar);
          debugPrint('directory.path      ${directory.path}');
          final taskId = await FlutterDownloader.enqueue(
            url: url,
            savedDir: directory.path,
            fileName: suggestedFilename,
            showNotification: true,
            openFileFromNotification: true,
            saveInPublicStorage: false,
            headers: {'Cookie': 'JSESSIONID=$sessionId'},
          );
          Get.back();
          debugPrint('Download enqueued with taskId: $taskId');
          ComponentUtils.showwarnpopup(
            title: 'Info',
            contenttext: 'file downloaded: $suggestedFilename',
            confirmBtn: 'Open',
            cancelBtn: 'Close',
            confirmfunction: () {
              debugPrint('taskId: $taskId');
              FlutterDownloader.open(taskId: taskId ?? '');
              Get.back();
            },
          );
        }
      } else {
        Get.back();
        ComponentUtils.showsnackbar(
          text: 'Unable to access storage',
          heading: 'Error',
          seconds: 2,
        );
      }
    } catch (e) {
      Get.back();
      debugPrint('Download error: $e');
      ComponentUtils.showsnackbar(
        text: 'Download error: $e',
        heading: 'Error',
        seconds: 2,
      );
    }
  }

  String extractFileName(String header) {
    // Find the part after 'filename="' and before the closing '"'
    const String prefix = 'filename="';
    int startIndex = header.indexOf(prefix) + prefix.length;
    int endIndex = header.indexOf('"', startIndex);
    return header.substring(startIndex, endIndex);
  }
}
